# Version 2 Technical Specification

## API Changes

### Authentication & Authorization
- JWT-based authentication
- Token format includes premium user flag
- Premium feature access control via decorators
- Token refresh mechanism

### Video Processing Endpoints

#### 1. /api/v2/video/convert
- Input: Video file, format options
- Output: Converted video
- Parameters:
  - output_format (MediaFormat)
  - width (Optional[int])
  - height (Optional[int])
  - fps (Optional[int])
  - remove_audio (bool)
  - video_codec (Optional[str])
  - audio_codec (Optional[str])
  - video_bitrate (Optional[str])
  - audio_bitrate (Optional[str])
  - start_time (Optional[float])
  - end_time (Optional[float])

#### 2. /api/v2/video/merge
- Input: Multiple video files
- Output: Single merged video
- Parameters:
  - files (List[UploadFile])
  - output_format (MediaFormat)

#### 3. /api/v2/video/extract-frames
- Input: Video file
- Output: Extracted frames
- Parameters:
  - fps (float)
  - output_format (MediaFormat)

#### 4. /api/v2/video/add-captions [Premium]
- Input: Video file, captions data
- Output: Captioned video
- Parameters:
  - captions (List[Caption])
  - output_format (MediaFormat)

#### 5. /api/v2/video/effects [Premium]
- Input: Video file, effects list
- Output: Processed video
- Parameters:
  - effects (List[VideoEffect])
  - output_format (MediaFormat)

### Audio Processing Endpoints

#### 1. /api/v2/audio/convert
- Input: Audio file, format options
- Output: Converted audio
- Parameters:
  - output_format (MediaFormat)
  - sample_rate (Optional[int])
  - channels (Optional[int])
  - audio_codec (Optional[str])
  - audio_bitrate (Optional[str])
  - start_time (Optional[float])
  - end_time (Optional[float])

#### 2. /api/v2/audio/merge
- Input: Multiple audio files
- Output: Single merged audio
- Parameters:
  - files (List[UploadFile])
  - output_format (MediaFormat)
  - crossfade (Optional[float])

#### 3. /api/v2/audio/extract
- Input: Video file
- Output: Extracted audio
- Parameters:
  - output_format (MediaFormat)

#### 4. /api/v2/audio/effects [Premium]
- Input: Audio file, effects list
- Output: Processed audio
- Parameters:
  - effects (List[AudioEffect])
  - output_format (MediaFormat)

## Data Models

### MediaFormat (Enum)
```python
class MediaFormat(str, Enum):
    # Video formats
    MP4 = "mp4"
    MOV = "mov"
    AVI = "avi"
    MKV = "mkv"
    
    # Audio formats
    MP3 = "mp3"
    WAV = "wav"
    AAC = "aac"
    FLAC = "flac"
    
    # Image formats
    JPG = "jpg"
    PNG = "png"
    GIF = "gif"
    WEBP = "webp"
```

### VideoEffect (Enum)
```python
class VideoEffect(str, Enum):
    BLUR = "blur"
    GRAYSCALE = "grayscale"
    SEPIA = "sepia"
```

### AudioEffect (Enum)
```python
class AudioEffect(str, Enum):
    ECHO = "echo"
    REVERB = "reverb"
```

### Caption Model
```python
class Caption(BaseModel):
    text: str
    start_time: float
    end_time: float
    position: Optional[str] = "bottom"
    font_size: Optional[int] = 32
    font_color: Optional[str] = "#ffffff"
    background_color: Optional[str] = "#000000"
    background_opacity: Optional[float] = 0.5
```

## Storage System

### Local Storage
- Temporary file storage
- Automatic cleanup
- Path: /tmp/media_processing/

### Cloud Storage
- AWS S3 integration
- Google Cloud Storage integration
- Configurable provider selection
- Automatic file lifecycle management

## Background Processing

### Job Queue
- Celery integration
- Redis backend
- Job status tracking
- Progress updates
- Error handling and retries

### Media Processing
- FFmpeg integration
- OpenCV for video processing
- PIL for image processing
- Concurrent processing support

## Error Handling

### HTTP Errors
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden (Premium features)
- 404: Not Found
- 422: Validation Error
- 500: Internal Server Error

### Custom Error Responses
```python
class ErrorResponse(BaseModel):
    detail: str
    code: str
    params: Optional[Dict[str, Any]] = None
```

## Configuration Management

### Environment Variables
- Application settings
- Security settings
- Storage settings
- Processing settings
- Premium feature flags

### Feature Flags
- Premium features
- Beta features
- Processing options
- Storage options

## Performance Considerations

### Caching
- Redis caching for frequent operations
- File caching strategy
- Cache invalidation rules

### Resource Management
- File size limits
- Processing timeouts
- Concurrent job limits
- Storage quotas

### Optimization
- Stream processing where possible
- Efficient file handling
- Memory usage optimization
- CPU usage optimization
