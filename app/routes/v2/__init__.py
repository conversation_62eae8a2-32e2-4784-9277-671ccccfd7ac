from app.routes.v2.video import router as video_router
from app.routes.v2.audio import router as audio_router
from app.routes.v2.image.image_overlay import router as image_overlay_router
from app.routes.v2.image.image_to_video import router as image_to_video_router
from app.routes.v2.media.transcription import router as media_transcription_router
from app.routes.v2.media.download import router as media_download_router

# Create consolidated routers
video = video_router
audio = audio_router

# Create image router with all image-related endpoints
from fastapi import APIRouter
image = APIRouter()
image.include_router(image_overlay_router, prefix="/overlay")
image.include_router(image_to_video_router, prefix="/to-video")

# Create media router with all media-related endpoints
media = APIRouter()
media.include_router(media_transcription_router, prefix="/transcription")
media.include_router(media_download_router, prefix="/download")
