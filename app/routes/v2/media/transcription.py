"""
Media transcription routes for v2 API.
"""
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel
from typing import Optional
from app.models.job import JobResponse, JobStatusResponse
from app.utils.storage import storage_provider
from app.utils.media_processor import MediaProcessor, MediaFormat
from app.utils.job_queue import job_queue, JobType
from app.utils.auth import get_current_user, User, require_premium

router = APIRouter()

class TranscriptionRequest(BaseModel):
    language: Optional[str] = "en"
    model: Optional[str] = "base"  # base or large
    timestamps: bool = False
    diarization: bool = False

@router.post("/start", response_model=JobResponse)
@require_premium()
async def start_transcription(
    file: UploadFile = File(...),
    language: str = "en",
    model: str = "base",
    timestamps: bool = False,
    diarization: bool = False,
    current_user: User = Depends(get_current_user)
):
    """Start a media transcription job."""
    file_path = None
    try:
        # Save uploaded file
        file_path = await storage_provider.save_upload(file, file.filename)
        
        # Create job with transcription settings
        job = await job_queue.create_job(
            job_type=JobType.MEDIA_TRANSCRIPTION,
            input_data={
                "file_path": file_path,
                "language": language,
                "model": model,
                "timestamps": timestamps,
                "diarization": diarization
            },
            user_id=current_user.username,
            is_premium=current_user.is_premium
        )
        
        return JobResponse(
            job_id=job.id,
            status=job.status,
            message="Transcription job created successfully"
        )
    except Exception as e:
        # Cleanup uploaded file if job creation fails
        if file_path:
            await storage_provider.delete_file(file_path)
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_transcription_status(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get the status of a transcription job."""
    try:
        job = await job_queue.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
            
        if job.user_id != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this job")
            
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            progress=job.progress,
            output=job.output_data,
            error=job.error
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/cancel/{job_id}")
async def cancel_transcription(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """Cancel a transcription job."""
    try:
        job = await job_queue.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
            
        if job.user_id != current_user.username:
            raise HTTPException(status_code=403, detail="Not authorized to access this job")
            
        success = await job_queue.cancel_job(job_id)
        if success:
            # Cleanup any temporary files
            if job.input_data and 'file_path' in job.input_data:
                await storage_provider.delete_file(job.input_data['file_path'])
            return {"message": "Job cancelled successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to cancel job")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
