from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, Depends
from app.models import ErrorResponse, JobResponse, JobStatusResponse
from app.utils.auth import require_premium
from app.utils.storage import storage_provider
from app.utils.image_overlay.controller import ImageOverlayController

router = APIRouter()
controller = ImageOverlayController()

@router.post("/", response_model=JobResponse)
async def create_image_overlay(
    image: UploadFile = File(...),
    overlay: UploadFile = File(...),
    position: str = Form("center"),
    scale: float = Form(1.0),
    opacity: float = Form(1.0),
    premium_user = Depends(require_premium)
) -> JobResponse:
    """Create an image with overlay."""
    try:
        # Save uploaded files
        image_path = await storage_provider.save_upload(image)
        overlay_path = await storage_provider.save_upload(overlay)
        
        # Process overlay
        job = await controller.process_overlay(
            image_path=image_path,
            overlay_path=overlay_path,
            position=position,
            scale=scale,
            opacity=opacity
        )
        
        return JobResponse(job_id=job.id)
        
    except Exception as e:
        return ErrorResponse(
            detail=str(e),
            code="IMAGE_OVERLAY_ERROR"
        )

@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_overlay_status(job_id: str) -> JobStatusResponse:
    """Get status of image overlay job."""
    try:
        status = await controller.get_job_status(job_id)
        return JobStatusResponse(
            status=status.status,
            result=status.result,
            error=status.error
        )
        
    except Exception as e:
        return ErrorResponse(
            detail=str(e),
            code="STATUS_CHECK_ERROR"
        )
