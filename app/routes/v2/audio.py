from typing import List, Optional

from fastapi import APIRouter, File, Form, UploadFile
from fastapi.responses import JSONResponse

from app.models import (
    MediaFormat,
    AudioEffect,
    ProcessingResult,
    StatusEnum
)
from app.utils.auth import require_premium
from app.utils.storage import storage_provider
from app.utils.media import media_processor

router = APIRouter()

@router.post("/convert")
async def convert_audio(
    file: UploadFile = File(...),
    output_format: MediaFormat = Form(MediaFormat.MP3),
    sample_rate: Optional[int] = Form(None),
    channels: Optional[int] = Form(None),
    audio_codec: Optional[str] = Form(None),
    audio_bitrate: Optional[str] = Form(None),
    start_time: Optional[float] = Form(None),
    end_time: Optional[float] = Form(None)
) -> ProcessingResult:
    """Convert audio to different format with options."""
    try:
        # Save uploaded file
        storage_info = await storage_provider.save_file(file)
        
        # Convert audio
        result = await media_processor.convert_audio(
            input_path=storage_info.url,
            output_format=output_format,
            sample_rate=sample_rate,
            channels=channels,
            audio_codec=audio_codec,
            audio_bitrate=audio_bitrate,
            start_time=start_time,
            end_time=end_time
        )
        
        # Cleanup input file
        await storage_provider.delete_file(storage_info.url)
        
        return result
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )

@router.post("/merge")
async def merge_audio(
    files: List[UploadFile] = File(...),
    output_format: MediaFormat = Form(MediaFormat.MP3),
    crossfade: Optional[float] = Form(None)
) -> ProcessingResult:
    """Merge multiple audio files."""
    try:
        # Save uploaded files
        storage_infos = []
        for file in files:
            storage_info = await storage_provider.save_file(file)
            storage_infos.append(storage_info)
        
        # Merge audio files
        result = await media_processor.merge_audio(
            input_paths=[info.url for info in storage_infos],
            output_format=output_format,
            crossfade=crossfade
        )
        
        # Cleanup input files
        for info in storage_infos:
            await storage_provider.delete_file(info.url)
        
        return result
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )

@router.post("/extract")
async def extract_audio(
    file: UploadFile = File(...),
    output_format: MediaFormat = Form(MediaFormat.MP3)
) -> ProcessingResult:
    """Extract audio from video."""
    try:
        # Save uploaded file
        storage_info = await storage_provider.save_file(file)
        
        # Extract audio
        result = await media_processor.extract_audio(
            input_path=storage_info.url,
            output_format=output_format
        )
        
        # Cleanup input file
        await storage_provider.delete_file(storage_info.url)
        
        return result
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )

@router.post("/effects")
@require_premium
async def apply_effects(
    file: UploadFile = File(...),
    effects: List[AudioEffect] = Form(...),
    output_format: MediaFormat = Form(MediaFormat.MP3)
) -> ProcessingResult:
    """Apply audio effects."""
    try:
        # Save uploaded file
        storage_info = await storage_provider.save_file(file)
        
        # Apply effects
        result = await media_processor.apply_audio_effects(
            input_path=storage_info.url,
            effects=effects,
            output_format=output_format
        )
        
        # Cleanup input file
        await storage_provider.delete_file(storage_info.url)
        
        return result
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )
