from typing import List, Optional

from fastapi import APIRouter, File, Form, UploadFile, Depends
from fastapi.responses import JSONResponse

from app.models import (
    MediaFormat,
    VideoEffect,
    Caption,
    ProcessingResult,
    StatusEnum
)
from app.utils.auth import require_premium
from app.utils.storage import storage_provider
from app.utils.media import media_processor
from app.utils.captions import caption_renderer

router = APIRouter()

@router.post("/convert")
async def convert_video(
    file: UploadFile = File(...),
    output_format: MediaFormat = Form(MediaFormat.MP4),
    width: Optional[int] = Form(None),
    height: Optional[int] = Form(None),
    fps: Optional[int] = Form(None),
    remove_audio: bool = Form(False),
    video_codec: Optional[str] = Form(None),
    audio_codec: Optional[str] = Form(None),
    video_bitrate: Optional[str] = Form(None),
    audio_bitrate: Optional[str] = Form(None),
    start_time: Optional[float] = Form(None),
    end_time: Optional[float] = Form(None)
) -> ProcessingResult:
    """Convert video to different format with options."""
    try:
        # Save uploaded file
        storage_info = await storage_provider.save_file(file)
        
        # Convert video
        result = await media_processor.convert_video(
            input_path=storage_info.url,
            output_format=output_format,
            width=width,
            height=height,
            fps=fps,
            remove_audio=remove_audio,
            video_codec=video_codec,
            audio_codec=audio_codec,
            video_bitrate=video_bitrate,
            audio_bitrate=audio_bitrate,
            start_time=start_time,
            end_time=end_time
        )
        
        # Cleanup input file
        await storage_provider.delete_file(storage_info.url)
        
        return result
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )

@router.post("/merge")
async def merge_videos(
    files: List[UploadFile] = File(...),
    output_format: MediaFormat = Form(MediaFormat.MP4)
) -> ProcessingResult:
    """Merge multiple videos."""
    try:
        # Save uploaded files
        storage_infos = []
        for file in files:
            storage_info = await storage_provider.save_file(file)
            storage_infos.append(storage_info)
        
        # Merge videos
        result = await media_processor.merge_videos(
            input_paths=[info.url for info in storage_infos],
            output_format=output_format
        )
        
        # Cleanup input files
        for info in storage_infos:
            await storage_provider.delete_file(info.url)
        
        return result
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )

@router.post("/extract-frames")
async def extract_frames(
    file: UploadFile = File(...),
    fps: float = Form(1.0),
    output_format: MediaFormat = Form(MediaFormat.JPG)
) -> ProcessingResult:
    """Extract frames from video."""
    try:
        # Save uploaded file
        storage_info = await storage_provider.save_file(file)
        
        # Extract frames
        result = await media_processor.extract_frames(
            input_path=storage_info.url,
            fps=fps,
            output_format=output_format
        )
        
        # Cleanup input file
        await storage_provider.delete_file(storage_info.url)
        
        return result
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )

@router.post("/add-captions")
@require_premium
async def add_captions(
    file: UploadFile = File(...),
    captions: List[Caption] = Form(...),
    output_format: MediaFormat = Form(MediaFormat.MP4)
) -> ProcessingResult:
    """Add captions to video."""
    try:
        # Save uploaded file
        storage_info = await storage_provider.save_file(file)
        
        # Add captions
        result = await caption_renderer.render_captions_on_video(
            input_path=storage_info.url,
            captions=captions
        )
        
        # Cleanup input file
        await storage_provider.delete_file(storage_info.url)
        
        return ProcessingResult(
            status=StatusEnum.COMPLETED,
            output_url=result
        )
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )

@router.post("/effects")
@require_premium
async def apply_effects(
    file: UploadFile = File(...),
    effects: List[VideoEffect] = Form(...),
    output_format: MediaFormat = Form(MediaFormat.MP4)
) -> ProcessingResult:
    """Apply video effects."""
    try:
        # Save uploaded file
        storage_info = await storage_provider.save_file(file)
        
        # Apply effects
        result = await media_processor.apply_video_effects(
            input_path=storage_info.url,
            effects=effects,
            output_format=output_format
        )
        
        # Cleanup input file
        await storage_provider.delete_file(storage_info.url)
        
        return result
        
    except Exception as e:
        return ProcessingResult(
            status=StatusEnum.FAILED,
            error=str(e)
        )
