# Dahopevi Media Processing API v2

A powerful API for video, audio, and image processing built with FastAPI and FFmpeg.

## Features

### Video Processing
- Convert videos to different formats
- Merge multiple videos
- Extract frames from videos
- Add captions to videos
- Apply video effects
- Customize video parameters (resolution, fps, codecs, etc.)

### Audio Processing
- Convert audio to different formats
- Merge multiple audio files
- Normalize audio levels
- Apply audio effects
- Customize audio parameters (sample rate, channels, bitrate, etc.)

### Storage Support
- Local filesystem storage
- Amazon S3 storage
- Google Cloud Storage

### Authentication & Authorization
- API key authentication
- JWT token authentication
- Role-based access control
- Premium features for subscribed users

### Background Processing
- Asynchronous job processing
- Job status tracking
- Job cancellation support
- Redis-based job queue

## Tech Stack

- Python 3.11
- FastAPI
- FFmpeg
- Celery
- Redis
- Docker
- Pydantic
- OpenCV
- Pillow

## Setup

### Prerequisites

- Python 3.11+
- FFmpeg
- Redis
- Docker & Docker Compose (optional)

### Local Development

1. Clone the repository:
```bash
git clone https://github.com/yourusername/dahopevi.git
cd dahopevi
```

2. Create and activate virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create .env file:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Run the application:
```bash
uvicorn app.main:app --reload
```

### Docker Deployment

1. Build and run with Docker Compose:
```bash
docker-compose up --build
```

The API will be available at `http://localhost:8000`.

## API Documentation

Once the server is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| SECRET_KEY | Secret key for JWT tokens | your-secret-key-here |
| STORAGE_TYPE | Storage backend (local/s3/gcp) | local |
| AWS_ACCESS_KEY_ID | AWS access key | None |
| AWS_SECRET_ACCESS_KEY | AWS secret key | None |
| AWS_REGION | AWS region | us-east-1 |
| S3_BUCKET | S3 bucket name | None |
| GCS_CREDENTIALS | Path to GCP credentials file | None |
| GCS_BUCKET | GCS bucket name | None |
| REDIS_HOST | Redis host | localhost |
| REDIS_PORT | Redis port | 6379 |
| REDIS_DB | Redis database number | 0 |
| REDIS_PASSWORD | Redis password | None |

## Directory Structure

```
dahopevi/
├── app/
│   ├── utils/          # Utility modules
│   ├── routes/         # API routes
│   ├── models.py       # Data models
│   ├── config.py       # Configuration
│   └── main.py         # Application entry point
├── docs/              # Documentation
├── fonts/             # Font files
├── scripts/           # Helper scripts
├── tests/             # Test suite
├── .env.example       # Example environment variables
├── docker-compose.yml # Docker Compose configuration
├── Dockerfile         # Docker configuration
└── requirements.txt   # Python dependencies
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
