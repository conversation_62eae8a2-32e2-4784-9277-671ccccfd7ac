#!/bin/bash

# Startup script for DahoPevi API
# This script handles both v1 (Flask) and v2 (FastAPI) endpoints

set -e

echo "Starting DahoPevi API..."

# Check if we should run v1 (Flask) or v2 (FastAPI) or both
API_VERSION=${API_VERSION:-"v2"}

case $API_VERSION in
    "v1")
        echo "Starting v1 API (Flask)..."
        exec gunicorn app:app --bind 0.0.0.0:8000 --workers 4 --timeout 300
        ;;
    "v2")
        echo "Starting v2 API (FastAPI)..."
        exec gunicorn app.main:app --bind 0.0.0.0:8000 --worker-class uvicorn.workers.UvicornWorker --workers 4 --timeout 300
        ;;
    "both")
        echo "Starting both v1 and v2 APIs..."
        # Start v1 on port 8001 in background
        gunicorn app:app --bind 0.0.0.0:8001 --workers 2 --timeout 300 &
        # Start v2 on port 8000 (main port)
        exec gunicorn app.main:app --bind 0.0.0.0:8000 --worker-class uvicorn.workers.UvicornWorker --workers 2 --timeout 300
        ;;
    *)
        echo "Invalid API_VERSION: $API_VERSION. Using v2 as default."
        exec gunicorn app.main:app --bind 0.0.0.0:8000 --worker-class uvicorn.workers.UvicornWorker --workers 4 --timeout 300
        ;;
esac
